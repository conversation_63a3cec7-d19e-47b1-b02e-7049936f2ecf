# Chikara Frontend JS/JSX Files Checklist

This document contains a checklist of all remaining `.js` and `.jsx` files in the `chikara-frontend/src` directory that may need to be migrated to TypeScript.

## Components

## Features

### Casino

- [ ] `features/casino/components/WheelSpin.jsx`

### Classroom

- [ ] `features/classroom/components/ClassChatroom.jsx`
- [ ] `features/classroom/components/ClassShop.jsx`
- [ ] `features/classroom/components/ClassTabs.jsx`
- [ ] `features/classroom/components/Exam.jsx`
- [ ] `features/classroom/components/ExamRanking.jsx`
- [ ] `features/classroom/components/ExamsTab.jsx`

### Daily Task

- [ ] `features/dailytask/components/DailyRewardDisplay.jsx`

### Faculty List

- [ ] `features/facultylist/ClassFilter.jsx`
- [ ] `features/facultylist/RoleFilter.jsx`

### Gang

- [ ] `features/gang/components/CreateGangModal.jsx`
- [ ] `features/gang/components/GangBanner.jsx`
- [ ] `features/gang/components/GangChatroom.jsx`
- [ ] `features/gang/components/GangHideout.jsx`
- [ ] `features/gang/components/GangInfo.jsx`
- [ ] `features/gang/components/GangInviteModal.jsx`
- [ ] `features/gang/components/GangLogDisplay.jsx`
- [ ] `features/gang/components/GangMemberModal.jsx`
- [ ] `features/gang/components/GangPayoutsInfo.jsx`
- [ ] `features/gang/components/GangSettingsModal.jsx`
- [ ] `features/gang/components/GangShop.jsx`
- [ ] `features/gang/components/ViewGangModal.jsx`
- [ ] `features/gang/components/YourGang.jsx`

### Home

- [ ] `features/home/<USER>/HomeCalendar.jsx`
- [ ] `features/home/<USER>/HomeNavButton.jsx`

### Hospital

- [ ] `features/hospital/components/HospitalInjuryPanel.jsx`
- [ ] `features/hospital/components/HospitalisationReason.jsx`

### News

- [ ] `features/news/components/ChangelogCard.jsx`
- [ ] `features/news/components/PostContent.jsx`
- [ ] `features/news/components/PublicNewsWrapper.jsx`

### Private Message

- [ ] `features/privatemessage/components/MessagePreview.jsx`
- [ ] `features/privatemessage/components/MessageWindow.jsx`
- [ ] `features/privatemessage/components/PrivateMessage.jsx`
- [ ] `features/privatemessage/components/SendMessageForm.jsx`

### Shop

- [ ] `features/shop/components/PurchaseItemModal.jsx`
- [ ] `features/shop/components/SellItemModal.jsx`
- [ ] `features/shop/components/ShopBuyItems.jsx`
- [ ] `features/shop/components/ShopItem.jsx`
- [ ] `features/shop/components/ShopItemSell.jsx`
- [ ] `features/shop/components/ShopSellItems.jsx`
- [ ] `features/shop/components/SingleShopkeeper.jsx`

### Streets

- [ ] `features/streets/components/EncounterView.jsx`
- [ ] `features/streets/components/LocationSelect.jsx`
- [ ] `features/streets/components/MapView.jsx`
- [ ] `features/streets/components/ReactFlow.jsx`
- [ ] `features/streets/components/ReactFlowNode.jsx`
- [ ] `features/streets/components/StartStreets.jsx`
- [ ] `features/streets/components/Streets.jsx`

### Suggestions

- [ ] `features/suggestions/components/SuggestionComments.jsx`

## Pages

- [ ] `pages/AdventurePage.jsx`
- [ ] `pages/Calendar.jsx`
- [ ] `pages/Casino.jsx`
- [ ] `pages/Classroom.jsx`
- [ ] `pages/CombinedExplore.jsx`
- [ ] `pages/ConstructionPage.jsx`
- [ ] `pages/Courses.jsx`
- [ ] `pages/DailyTaskPage.jsx`
- [ ] `pages/Events.jsx`
- [ ] `pages/Explore.jsx`
- [ ] `pages/FullChat.jsx`
- [ ] `pages/GameStats.jsx`

## Migration Notes

Convert the following javascript (.jsx) files to Typescript.

- Remember that api responses are already fully typed.
- Do not use return types.

When migrating these files to TypeScript:

1. Rename file extensions from `.jsx` to `.tsx`
2. Add proper TypeScript types for props, state, and function parameters
3. Update any import statements that may need type adjustments
